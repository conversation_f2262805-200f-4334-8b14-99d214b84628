package main

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/hmac"
	"crypto/rand"
	"crypto/sha1"
	"database/sql"
	"encoding/base32"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/joho/godotenv"
	_ "github.com/lib/pq"
)

// Request structures matching Java models
type TrnsSalesSaveWrReq struct {
	Tin          string                  `json:"tin"`
	BhfId        string                  `json:"bhfId"`
	InvcNo       int64                   `json:"invcNo"`
	OrgInvcNo    *int64                  `json:"orgInvcNo,omitempty"`
	CustTin      string                  `json:"custTin"`
	CustNm       string                  `json:"custNm"`
	SalesTyCd    string                  `json:"salesTyCd"`
	RcptTyCd     string                  `json:"rcptTyCd"`
	PmtTyCd      string                  `json:"pmtTyCd"`
	SalesSttsCd  string                  `json:"salesSttsCd"`
	CfmDt        string                  `json:"cfmDt"`
	SalesDt      string                  `json:"salesDt"`
	StockRlsDt   string                  `json:"stockRlsDt"`
	CnclReqDt    string                  `json:"cnclReqDt"`
	CnclDt       string                  `json:"cnclDt"`
	RfdDt        string                  `json:"rfdDt"`
	RfdRsnCd     string                  `json:"rfdRsnCd"`
	TotItemCnt   int                     `json:"totItemCnt"`
	TaxblAmtA    float64                 `json:"taxblAmtA"`
	TaxblAmtB    float64                 `json:"taxblAmtB"`
	TaxblAmtC    float64                 `json:"taxblAmtC"`
	TaxblAmtD    float64                 `json:"taxblAmtD"`
	TaxblAmtE    float64                 `json:"taxblAmtE"`
	TaxRtA       float64                 `json:"taxRtA"`
	TaxRtB       float64                 `json:"taxRtB"`
	TaxRtC       float64                 `json:"taxRtC"`
	TaxRtD       float64                 `json:"taxRtD"`
	TaxRtE       float64                 `json:"taxRtE"`
	TaxAmtA      float64                 `json:"taxAmtA"`
	TaxAmtB      float64                 `json:"taxAmtB"`
	TaxAmtC      float64                 `json:"taxAmtC"`
	TaxAmtD      float64                 `json:"taxAmtD"`
	TaxAmtE      float64                 `json:"taxAmtE"`
	TotTaxblAmt  float64                 `json:"totTaxblAmt"`
	TotTaxAmt    float64                 `json:"totTaxAmt"`
	TotAmt       float64                 `json:"totAmt"`
	PrchrAcptcYn string                  `json:"prchrAcptcYn"`
	Remark       string                  `json:"remark"`
	RegrId       string                  `json:"regrId"`
	RegrNm       string                  `json:"regrNm"`
	ModrId       string                  `json:"modrId"`
	ModrNm       string                  `json:"modrNm"`
	Receipt      *TrnsSalesSaveWrReceipt `json:"receipt"`
	ItemList     []TrnsSalesSaveWrItem   `json:"itemList"`
}

type TrnsSalesSaveWrReceipt struct {
	CurRcptNo    *int64 `json:"curRcptNo,omitempty"`
	TotRcptNo    *int64 `json:"totRcptNo,omitempty"`
	CustTin      string `json:"custTin"`
	CustMblNo    string `json:"custMblNo"`
	RptNo        *int64 `json:"rptNo,omitempty"`
	RcptPbctDt   string `json:"rcptPbctDt"`
	IntrlData    string `json:"intrlData"`
	RcptSign     string `json:"rcptSign"`
	Jrnl         string `json:"jrnl"`
	TrdeNm       string `json:"trdeNm"`
	Adrs         string `json:"adrs"`
	TopMsg       string `json:"topMsg"`
	BtmMsg       string `json:"btmMsg"`
	PrchrAcptcYn string `json:"prchrAcptcYn"`
}

type TrnsSalesSaveWrItem struct {
	ItemSeq   int     `json:"itemSeq"`
	ItemCd    string  `json:"itemCd"`
	ItemClsCd string  `json:"itemClsCd"`
	ItemNm    string  `json:"itemNm"`
	Bcd       string  `json:"bcd"`
	PkgUnitCd string  `json:"pkgUnitCd"`
	Pkg       float64 `json:"pkg"`
	QtyUnitCd string  `json:"qtyUnitCd"`
	Qty       float64 `json:"qty"`
	Prc       float64 `json:"prc"`
	SplyAmt   float64 `json:"splyAmt"`
	DcRt      float64 `json:"dcRt"`
	DcAmt     float64 `json:"dcAmt"`
	IsrccCd   string  `json:"isrccCd"`
	IsrccNm   string  `json:"isrccNm"`
	IsrcRt    string  `json:"isrcRt"`
	IsrcAmt   string  `json:"isrcAmt"`
	TaxTyCd   string  `json:"taxTyCd"`
	TaxblAmt  float64 `json:"taxblAmt"`
	TaxAmt    float64 `json:"taxAmt"`
	TotAmt    float64 `json:"totAmt"`
}

// Response structures
type TrnsSalesSaveWrRes struct {
	ResultCd  string                  `json:"resultCd"`
	ResultMsg string                  `json:"resultMsg"`
	ResultDt  string                  `json:"resultDt"`
	Data      *TrnsSalesSaveWrResData `json:"data,omitempty"`
}

type TrnsSalesSaveWrResData struct {
	Info             *Info  `json:"info,omitempty"`
	IntrlData        string `json:"intrlData"`
	MrcNo            string `json:"mrcNo"`
	RcptNo           *int64 `json:"rcptNo,omitempty"`
	RcptSign         string `json:"rcptSign"`
	ResultDt         string `json:"resultDt"`
	SdcId            string `json:"sdcId"`
	TotRcptNo        *int64 `json:"totRcptNo,omitempty"`
	VsdcRcptPbctDate string `json:"vsdcRcptPbctDate"`
}

type Info struct {
	BhfOpenDt        string `json:"bhfOpenDt"`
	CmcKey           string `json:"cmcKey"`
	IntrlKey         string `json:"intrlKey"`
	LastCopyInvcNo   *int64 `json:"lastCopyInvcNo,omitempty"`
	LastInvcNo       *int64 `json:"lastInvcNo,omitempty"`
	LastProfrmInvcNo *int64 `json:"lastProfrmInvcNo,omitempty"`
	LastSaleInvcNo   *int64 `json:"lastSaleInvcNo,omitempty"`
	LastSaleRcptNo   *int64 `json:"lastSaleRcptNo,omitempty"`
	LastTrainInvcNo  *int64 `json:"lastTrainInvcNo,omitempty"`
	MrcNo            string `json:"mrcNo"`
	SdcId            string `json:"sdcId"`
	SignKey          string `json:"signKey"`
}

// Device data structures for caching
type DeviceData struct {
	DvcSrlNo string
	SdcId    string
	MrcNo    string
	LoadTime time.Time
}

type CryptoKeys struct {
	IntrlKey string
	SignKey  string
	CmcKey   string
	HexKey   string
	LoadTime time.Time
}

// Global caches for ultra-fast performance
var (
	deviceCache     = make(map[string]string)
	keyCache        = make(map[string]string)
	hexKeyCache     = make(map[string]string)
	preloadedDevice = make(map[string]*DeviceData)
	preloadedCrypto = make(map[string]*CryptoKeys)
	cacheMutex      = sync.RWMutex{}
	sequenceCounter = int64(1000) // Simple counter for demo

	// Configuration flags
	enableETIMSPosting = true // Set to false for testing without posting to ETIMS

	// Database connection
	db *sql.DB
)

// Constants matching Java implementation
const (
	SALES_TYPE_N               = "N"
	SALES_TYPE_T               = "T"
	SALES_TYPE_P               = "P"
	SALES_TYPE_C               = "C"
	RECEIPT_TYPE_S             = "S"
	RECEIPT_TYPE_R             = "R"
	SERIAL_INV_NSR             = "serlInvNsr"
	SERIAL_INV_TSR             = "serlInvTsr"
	SERIAL_INV_PS              = "serlInvPs"
	SERIAL_INV_CSR             = "serlInvCsr"
	RESULT_CODE_SUCCESS        = "000"
	RESULT_CODE_TYPE_ERROR     = "834"
	RESULT_CODE_SEQUENCE_ERROR = "836"
	RESULT_CODE_GENERAL_ERROR  = "899"

	// ETIMS API Configuration
	ETIMS_API_BASE_URL        = "https://etims-api-sbx.kra.go.ke/etims-api"
	ETIMS_SAVE_SALES_ENDPOINT = "/saveTrnsSalesVsdc"
	HTTP_TIMEOUT_SECONDS      = 30

	// ETIMS API Headers
	ETIMS_API_VERSION = "1.0"
	ETIMS_CLIENT_ID   = "VSDC_CLIENT"
)

// Pre-initialized error responses
var (
	typeErrorResponse = &TrnsSalesSaveWrRes{
		ResultCd:  RESULT_CODE_TYPE_ERROR,
		ResultMsg: "SalesType and ReceiptType must be NS-NR-TS-TR-CS-CR-PS check your inputs.",
	}
	sequenceErrorResponse = &TrnsSalesSaveWrRes{
		ResultCd:  RESULT_CODE_SEQUENCE_ERROR,
		ResultMsg: "Your Sequences have been altered, Connect to KRA API to get Sequences.",
	}
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Printf("Warning: Error loading .env file: %v", err)
	}

	// Initialize database connection
	if err := initDatabase(); err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer db.Close()

	// Pre-load device data on startup
	preloadAllData()

	// Setup HTTP handlers
	http.HandleFunc("/trnsSales/saveSales", saveTrnsSalesHandler)
	http.HandleFunc("/trnsSales/preloadedPaths", getPreloadedPathsHandler)
	http.HandleFunc("/trnsSales/addDevicePath", addDevicePathHandler)
	http.HandleFunc("/trnsSales/config", configHandler)
	http.HandleFunc("/trnsSales/testHeaders", testHeadersHandler)
	http.HandleFunc("/health", healthHandler)

	log.Println("🚀 Go ETIMS Signer Server starting on :8088")
	log.Println("📊 Ultra-fast performance mode enabled")
	log.Printf("🌐 ETIMS API posting: %t", enableETIMSPosting)
	log.Println("📋 Available endpoints:")
	log.Println("   POST /trnsSales/saveSales - Main sales transaction endpoint")
	log.Println("   GET  /trnsSales/preloadedPaths - View pre-loaded device paths")
	log.Println("   POST /trnsSales/addDevicePath?tinBhfPath=X - Add device path")
	log.Println("   GET  /trnsSales/config - View configuration")
	log.Println("   POST /trnsSales/config - Update configuration")
	log.Println("   POST /trnsSales/testHeaders - Test ETIMS headers generation")
	log.Println("   GET  /health - Health check")

	log.Fatal(http.ListenAndServe(":8088", nil))
}

// Initialize database connection and create tables
func initDatabase() error {
	databaseURL := os.Getenv("DATABASE_URL")
	if databaseURL == "" {
		return fmt.Errorf("DATABASE_URL environment variable is not set")
	}

	var err error
	db, err = sql.Open("postgres", databaseURL)
	if err != nil {
		return fmt.Errorf("failed to open database connection: %v", err)
	}

	// Test the connection
	if err = db.Ping(); err != nil {
		return fmt.Errorf("failed to ping database: %v", err)
	}

	// Create tables if they don't exist
	if err = createTables(); err != nil {
		return fmt.Errorf("failed to create tables: %v", err)
	}

	log.Println("✅ Database connection established successfully")
	return nil
}

// Create database tables
func createTables() error {
	// Create sales transactions table
	createSalesTable := `
	CREATE TABLE IF NOT EXISTS trns_sales (
		id SERIAL PRIMARY KEY,
		tin VARCHAR(20) NOT NULL,
		bhf_id VARCHAR(10) NOT NULL,
		invc_no BIGINT NOT NULL,
		org_invc_no BIGINT,
		cust_tin VARCHAR(20),
		cust_nm VARCHAR(255),
		sales_ty_cd VARCHAR(10),
		rcpt_ty_cd VARCHAR(10),
		pmt_ty_cd VARCHAR(10),
		sales_stts_cd VARCHAR(10),
		cfm_dt VARCHAR(20),
		sales_dt VARCHAR(20),
		stock_rel_yn VARCHAR(1),
		tot_item_cnt INTEGER,
		taxbl_amt_a DECIMAL(15,2),
		taxbl_amt_b DECIMAL(15,2),
		taxbl_amt_c DECIMAL(15,2),
		taxbl_amt_d DECIMAL(15,2),
		tax_amt_a DECIMAL(15,2),
		tax_amt_b DECIMAL(15,2),
		tax_amt_c DECIMAL(15,2),
		tax_amt_d DECIMAL(15,2),
		tot_taxbl_amt DECIMAL(15,2),
		tot_tax_amt DECIMAL(15,2),
		tot_amt DECIMAL(15,2),
		prc_ord_dc_amt DECIMAL(15,2),
		disc_ty_cd VARCHAR(10),
		disc_rt DECIMAL(5,2),
		disc_amt DECIMAL(15,2),
		surch_ty_cd VARCHAR(10),
		surch_rt DECIMAL(5,2),
		surch_amt DECIMAL(15,2),
		tot_dc_amt DECIMAL(15,2),
		tot_rfd_amt DECIMAL(15,2),
		net_amt DECIMAL(15,2),
		prc_uprc DECIMAL(15,2),
		regr_id VARCHAR(50),
		regr_nm VARCHAR(255),
		modr_id VARCHAR(50),
		modr_nm VARCHAR(255),
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
		updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
		UNIQUE(tin, bhf_id, invc_no)
	);`

	if _, err := db.Exec(createSalesTable); err != nil {
		return fmt.Errorf("failed to create trns_sales table: %v", err)
	}

	// Create sales items table
	createItemsTable := `
	CREATE TABLE IF NOT EXISTS trns_sales_items (
		id SERIAL PRIMARY KEY,
		sales_id INTEGER REFERENCES trns_sales(id) ON DELETE CASCADE,
		tin VARCHAR(20) NOT NULL,
		bhf_id VARCHAR(10) NOT NULL,
		invc_no BIGINT NOT NULL,
		item_seq INTEGER NOT NULL,
		item_cd VARCHAR(50),
		item_cls_cd VARCHAR(10),
		item_nm VARCHAR(255),
		bcd VARCHAR(50),
		pkg_unit_cd VARCHAR(10),
		pkg DECIMAL(15,3),
		qty_unit_cd VARCHAR(10),
		qty DECIMAL(15,3),
		prc DECIMAL(15,2),
		sply_amt DECIMAL(15,2),
		dc_rt DECIMAL(5,2),
		dc_amt DECIMAL(15,2),
		isrcc_cd VARCHAR(10),
		isrcc_nm VARCHAR(255),
		isrc_rt VARCHAR(10),
		isrc_amt VARCHAR(20),
		tax_ty_cd VARCHAR(10),
		taxbl_amt DECIMAL(15,2),
		tax_amt DECIMAL(15,2),
		tot_amt DECIMAL(15,2),
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
	);`

	if _, err := db.Exec(createItemsTable); err != nil {
		return fmt.Errorf("failed to create trns_sales_items table: %v", err)
	}

	// Create receipt data table
	createReceiptTable := `
	CREATE TABLE IF NOT EXISTS trns_sales_receipts (
		id SERIAL PRIMARY KEY,
		sales_id INTEGER REFERENCES trns_sales(id) ON DELETE CASCADE,
		tin VARCHAR(20) NOT NULL,
		bhf_id VARCHAR(10) NOT NULL,
		invc_no BIGINT NOT NULL,
		cur_rcpt_no BIGINT,
		tot_rcpt_no BIGINT,
		cust_tin VARCHAR(20),
		cust_mbl_no VARCHAR(20),
		rpt_no BIGINT,
		rcpt_pbct_dt VARCHAR(20),
		intrl_data TEXT,
		rcpt_sign TEXT,
		jrnl TEXT,
		trde_nm VARCHAR(255),
		adrs TEXT,
		top_msg TEXT,
		btm_msg TEXT,
		prchr_acptc_yn VARCHAR(1),
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
	);`

	if _, err := db.Exec(createReceiptTable); err != nil {
		return fmt.Errorf("failed to create trns_sales_receipts table: %v", err)
	}

	log.Println("✅ Database tables created/verified successfully")
	return nil
}

// Save sales transaction data to database (matching Java TrnsSalesExecute.java implementation)
func saveSalesToDatabase(req *TrnsSalesSaveWrReq, deviceData *DeviceData, intrlData, signData, vsdcRcptPbctDate string) (int64, error) {
	// Begin transaction
	tx, err := db.Begin()
	if err != nil {
		return 0, fmt.Errorf("failed to begin transaction: %v", err)
	}
	defer tx.Rollback()

	// Insert main sales record
	var salesId int64
	salesQuery := `
		INSERT INTO trns_sales (
			tin, bhf_id, invc_no, org_invc_no, cust_tin, cust_nm, sales_ty_cd, rcpt_ty_cd, pmt_ty_cd,
			sales_stts_cd, cfm_dt, sales_dt, tot_item_cnt, taxbl_amt_a, taxbl_amt_b,
			taxbl_amt_c, taxbl_amt_d, tax_amt_a, tax_amt_b, tax_amt_c, tax_amt_d, tot_taxbl_amt,
			tot_tax_amt, tot_amt, regr_id, regr_nm, modr_id, modr_nm
		) VALUES (
			$1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19,
			$20, $21, $22, $23, $24, $25, $26, $27, $28
		) RETURNING id`

	err = tx.QueryRow(salesQuery,
		req.Tin, req.BhfId, req.InvcNo, req.OrgInvcNo, req.CustTin, req.CustNm, req.SalesTyCd,
		req.RcptTyCd, req.PmtTyCd, req.SalesSttsCd, req.CfmDt, req.SalesDt,
		req.TotItemCnt, req.TaxblAmtA, req.TaxblAmtB, req.TaxblAmtC, req.TaxblAmtD,
		req.TaxAmtA, req.TaxAmtB, req.TaxAmtC, req.TaxAmtD, req.TotTaxblAmt, req.TotTaxAmt,
		req.TotAmt, req.RegrId, req.RegrNm, req.ModrId, req.ModrNm,
	).Scan(&salesId)

	if err != nil {
		return 0, fmt.Errorf("failed to insert sales record: %v", err)
	}

	// Insert receipt data if available
	if req.Receipt != nil {
		receiptQuery := `
			INSERT INTO trns_sales_receipts (
				sales_id, tin, bhf_id, invc_no, cur_rcpt_no, tot_rcpt_no, cust_tin, cust_mbl_no,
				rpt_no, rcpt_pbct_dt, intrl_data, rcpt_sign, jrnl, trde_nm, adrs, top_msg,
				btm_msg, prchr_acptc_yn
			) VALUES (
				$1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18
			)`

		_, err = tx.Exec(receiptQuery,
			salesId, req.Tin, req.BhfId, req.InvcNo, req.Receipt.CurRcptNo, req.Receipt.TotRcptNo,
			req.Receipt.CustTin, req.Receipt.CustMblNo, req.Receipt.RptNo, vsdcRcptPbctDate,
			intrlData, signData, req.Receipt.Jrnl, req.Receipt.TrdeNm, req.Receipt.Adrs,
			req.Receipt.TopMsg, req.Receipt.BtmMsg, req.Receipt.PrchrAcptcYn,
		)

		if err != nil {
			return 0, fmt.Errorf("failed to insert receipt record: %v", err)
		}
	}

	// Insert item data
	if len(req.ItemList) > 0 {
		itemQuery := `
			INSERT INTO trns_sales_items (
				sales_id, tin, bhf_id, invc_no, item_seq, item_cd, item_cls_cd, item_nm, bcd,
				pkg_unit_cd, pkg, qty_unit_cd, qty, prc, sply_amt, dc_rt, dc_amt, isrcc_cd,
				isrcc_nm, isrc_rt, isrc_amt, tax_ty_cd, taxbl_amt, tax_amt, tot_amt
			) VALUES (
				$1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18,
				$19, $20, $21, $22, $23, $24, $25
			)`

		for _, item := range req.ItemList {
			_, err = tx.Exec(itemQuery,
				salesId, req.Tin, req.BhfId, req.InvcNo, item.ItemSeq, item.ItemCd, item.ItemClsCd,
				item.ItemNm, item.Bcd, item.PkgUnitCd, item.Pkg, item.QtyUnitCd, item.Qty,
				item.Prc, item.SplyAmt, item.DcRt, item.DcAmt, item.IsrccCd, item.IsrccNm,
				item.IsrcRt, item.IsrcAmt, item.TaxTyCd, item.TaxblAmt, item.TaxAmt, item.TotAmt,
			)

			if err != nil {
				return 0, fmt.Errorf("failed to insert item record for item %d: %v", item.ItemSeq, err)
			}
		}
	}

	// Commit transaction
	if err = tx.Commit(); err != nil {
		return 0, fmt.Errorf("failed to commit transaction: %v", err)
	}

	return salesId, nil
}

// Pre-load all device data for ultra-fast performance
func preloadAllData() {
	commonPaths := []string{
		"P051922564N_00", "P051922564N_01",
		"P051468536N_00", "P051468536N_01",
	}

	for _, tinBhfPath := range commonPaths {
		if err := preloadDeviceData(tinBhfPath); err != nil {
			log.Printf("⚠️  Failed to preload device data for %s: %v", tinBhfPath, err)
		} else {
			log.Printf("✅ Successfully pre-loaded device data for: %s", tinBhfPath)
		}

		if err := preloadCryptoKeys(tinBhfPath); err != nil {
			log.Printf("⚠️  Failed to preload crypto keys for %s: %v", tinBhfPath, err)
		} else {
			log.Printf("✅ Successfully pre-loaded crypto keys for: %s", tinBhfPath)
		}
	}

	log.Printf("🎯 Data pre-loading completed. Loaded %d device configurations.", len(commonPaths))
}

// Simulate device data loading (replace with actual file/DB operations)
func preloadDeviceData(tinBhfPath string) error {
	cacheMutex.Lock()
	defer cacheMutex.Unlock()

	// Simulate device data (replace with actual data loading)
	deviceData := &DeviceData{
		DvcSrlNo: "DEV" + tinBhfPath,
		SdcId:    "SDC" + tinBhfPath,
		MrcNo:    "MRC" + tinBhfPath,
		LoadTime: time.Now(),
	}

	preloadedDevice[tinBhfPath] = deviceData

	// Also populate regular cache
	deviceCache["dvcSrlNo_"+tinBhfPath] = deviceData.DvcSrlNo
	deviceCache["sdcId_"+tinBhfPath] = deviceData.SdcId
	deviceCache["mrcNo_"+tinBhfPath] = deviceData.MrcNo

	return nil
}

// Simulate crypto keys loading (replace with actual key loading)
func preloadCryptoKeys(tinBhfPath string) error {
	cacheMutex.Lock()
	defer cacheMutex.Unlock()

	// Simulate crypto keys (replace with actual key loading)
	intrlKey := "INTRL" + tinBhfPath + "KEY"
	signKey := "SIGN" + tinBhfPath + "KEY"
	cmcKey := "CMC" + tinBhfPath + "KEY"

	// Convert to hex (simplified - replace with actual Base32 conversion)
	hexKey := hex.EncodeToString([]byte(intrlKey))

	cryptoKeys := &CryptoKeys{
		IntrlKey: intrlKey,
		SignKey:  signKey,
		CmcKey:   cmcKey,
		HexKey:   strings.ToUpper(hexKey),
		LoadTime: time.Now(),
	}

	preloadedCrypto[tinBhfPath] = cryptoKeys

	// Also populate regular caches
	keyCache["intrlKey_"+tinBhfPath] = intrlKey
	keyCache["signKey_"+tinBhfPath] = signKey
	keyCache["cmcKey_"+tinBhfPath] = cmcKey
	hexKeyCache[intrlKey] = cryptoKeys.HexKey

	return nil
}

// Ultra-fast device data retrieval
func getDeviceDataUltraFast(tinBhfPath string) (*DeviceData, error) {
	cacheMutex.RLock()
	deviceData := preloadedDevice[tinBhfPath]
	cacheMutex.RUnlock()

	if deviceData != nil {
		return deviceData, nil
	}

	// Fallback: load dynamically
	log.Printf("⚠️ Device data not pre-loaded for %s. Loading dynamically.", tinBhfPath)
	if err := preloadDeviceData(tinBhfPath); err != nil {
		return nil, fmt.Errorf("failed to load device data: %v", err)
	}

	cacheMutex.RLock()
	deviceData = preloadedDevice[tinBhfPath]
	cacheMutex.RUnlock()

	return deviceData, nil
}

// Ultra-fast crypto keys retrieval
func getCryptoKeysUltraFast(tinBhfPath string) (*CryptoKeys, error) {
	cacheMutex.RLock()
	cryptoKeys := preloadedCrypto[tinBhfPath]
	cacheMutex.RUnlock()

	if cryptoKeys != nil {
		return cryptoKeys, nil
	}

	// Fallback: load dynamically
	log.Printf("⚠️ Crypto keys not pre-loaded for %s. Loading dynamically.", tinBhfPath)
	if err := preloadCryptoKeys(tinBhfPath); err != nil {
		return nil, fmt.Errorf("failed to load crypto keys: %v", err)
	}

	cacheMutex.RLock()
	cryptoKeys = preloadedCrypto[tinBhfPath]
	cacheMutex.RUnlock()

	return cryptoKeys, nil
}

// Generate next sequence number (simplified for demo)
func getNextSequenceNumber() int64 {
	cacheMutex.Lock()
	defer cacheMutex.Unlock()
	sequenceCounter++
	return sequenceCounter
}

// Generate internal data (matching Java implementation with AES encryption and Base32 encoding)
func generateInternalData(salesTyCd, rcptTyCd string, taxTyBTaxAmt float64, rptNo, totInvcNoCnt int64, hexKey string) (string, error) {
	intNSTaxAmtB := 0
	intNRTaxAmtB := 0

	if salesTyCd == "N" {
		if rcptTyCd == "S" {
			intNSTaxAmtB = int(taxTyBTaxAmt)
		} else {
			intNRTaxAmtB = int(taxTyBTaxAmt)
		}
	}

	intTaxTyBTaxAmt := int(taxTyBTaxAmt)
	intNRTaxAmtB += intTaxTyBTaxAmt

	// Build internal data string (matching Java format)
	internalData := fmt.Sprintf("%010d%010d%04d%08d", intNSTaxAmtB, intNRTaxAmtB, rptNo, totInvcNoCnt)

	// Convert hex key to bytes for AES encryption
	keyBytes, err := hex.DecodeString(hexKey)
	if err != nil {
		return "", fmt.Errorf("failed to decode hex key: %v", err)
	}

	// Ensure key is 32 bytes for AES-256 (pad or truncate if necessary)
	if len(keyBytes) < 32 {
		// Pad with zeros if too short
		padded := make([]byte, 32)
		copy(padded, keyBytes)
		keyBytes = padded
	} else if len(keyBytes) > 32 {
		// Truncate if too long
		keyBytes = keyBytes[:32]
	}

	// Encrypt using AES
	encrypted, err := encryptAES([]byte(internalData), keyBytes)
	if err != nil {
		return "", fmt.Errorf("failed to encrypt internal data: %v", err)
	}

	// Encode to Base32 (matching Java implementation)
	base32Encoded := base32.StdEncoding.EncodeToString(encrypted)

	// Remove padding characters to match Java output format
	base32Encoded = strings.TrimRight(base32Encoded, "=")

	return base32Encoded, nil
}

// AES encryption function (matching Java AES implementation)
func encryptAES(plaintext, key []byte) ([]byte, error) {
	// Create AES cipher
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}

	// Pad plaintext to be multiple of block size
	plaintext = pkcs7Pad(plaintext, aes.BlockSize)

	// Create IV (initialization vector)
	iv := make([]byte, aes.BlockSize)
	if _, err := io.ReadFull(rand.Reader, iv); err != nil {
		return nil, err
	}

	// Create CBC mode cipher
	mode := cipher.NewCBCEncrypter(block, iv)

	// Encrypt the data
	ciphertext := make([]byte, len(plaintext))
	mode.CryptBlocks(ciphertext, plaintext)

	// Prepend IV to ciphertext (Java style)
	result := append(iv, ciphertext...)
	return result, nil
}

// PKCS7 padding function
func pkcs7Pad(data []byte, blockSize int) []byte {
	padding := blockSize - (len(data) % blockSize)
	padText := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(data, padText...)
}

// Generate signature using HMAC-SHA1 (matching Java implementation)
func generateSignature(rcptDt, tin, custTin string, invcNo int64, taxTyBTaxblAmt, taxTyBTaxAmt, taxTyATaxblAmt, taxTyATaxAmt float64, salesTyCd, rcptTyCd string, totInvcNoCnt, salesTyTotInvcNoCnt int64, mrcNo, sdcId, signKey string) (string, error) {

	// Build signature string (matching Java format)
	var sb strings.Builder
	sb.WriteString(rcptDt)
	sb.WriteString(tin)
	if custTin != "" {
		sb.WriteString(custTin)
	}
	sb.WriteString(fmt.Sprintf("%010d", invcNo))
	sb.WriteString(mrcNo)
	sb.WriteString(fmt.Sprintf("%015.2f", taxTyBTaxblAmt))
	sb.WriteString(fmt.Sprintf("%015.2f", taxTyBTaxAmt))
	sb.WriteString(fmt.Sprintf("%015.2f", taxTyATaxblAmt))
	sb.WriteString(fmt.Sprintf("%015.2f", taxTyATaxAmt))
	// Add zero amounts for other tax types
	for i := 0; i < 6; i++ {
		sb.WriteString("000000000000,00")
	}
	sb.WriteString(salesTyCd)
	sb.WriteString(rcptTyCd)
	sb.WriteString(sdcId)
	sb.WriteString(rcptDt)
	sb.WriteString(fmt.Sprintf("%010d", totInvcNoCnt))
	sb.WriteString(fmt.Sprintf("%010d", salesTyTotInvcNoCnt))

	// Generate HMAC-SHA1 signature
	h := hmac.New(sha1.New, []byte(signKey))
	h.Write([]byte(sb.String()))
	signature := h.Sum(nil)

	// Encode to base32 (matching Java Base32 implementation)
	return base32.StdEncoding.EncodeToString(signature), nil
}

// Format current time to match Java VsdcUtil.DateStringFormater
func formatCurrentTime() string {
	return time.Now().Format("20060102150405")
}

// HTTP client for ETIMS API calls
var etimsHTTPClient = &http.Client{
	Timeout: HTTP_TIMEOUT_SECONDS * time.Second,
}

// Generate authentication headers for ETIMS API
func generateETIMSAuthHeaders(req *TrnsSalesSaveWrReq) map[string]string {
	headers := make(map[string]string)

	// Basic required headers
	headers["tin"] = req.Tin
	headers["bhfId"] = req.BhfId
	headers["tinBhfPath"] = req.Tin + "_" + req.BhfId // Combined TIN and BHF path
	headers["API-Version"] = ETIMS_API_VERSION
	headers["Client-ID"] = ETIMS_CLIENT_ID
	headers["Request-ID"] = fmt.Sprintf("%s-%s-%d-%d", req.Tin, req.BhfId, req.InvcNo, time.Now().Unix())
	headers["Timestamp"] = formatCurrentTime()

	// Additional headers that might be required by ETIMS
	headers["X-Requested-With"] = "XMLHttpRequest"
	headers["Cache-Control"] = "no-cache"
	headers["Pragma"] = "no-cache"
	headers["Connection"] = "keep-alive"

	// Common ETIMS headers based on typical implementations
	headers["dvcSrlNo"] = "DEV" + req.Tin + "_" + req.BhfId // Device serial number
	headers["userId"] = "SYSTEM"                            // User ID
	headers["requestDt"] = formatCurrentTime()              // Request date/time

	return headers
}

// Post sales data to ETIMS API with robust error handling
func postSalesToETIMS(req *TrnsSalesSaveWrReq) (*TrnsSalesSaveWrRes, error) {
	// Convert request to JSON
	jsonData, err := json.Marshal(req)
	if err != nil {
		log.Printf("ERROR: Failed to marshal request to JSON: %v", err)
		return nil, fmt.Errorf("failed to marshal request: %v", err)
	}

	// Create HTTP request
	url := ETIMS_API_BASE_URL + ETIMS_SAVE_SALES_ENDPOINT
	httpReq, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		log.Printf("ERROR: Failed to create HTTP request: %v", err)
		return nil, fmt.Errorf("failed to create HTTP request: %v", err)
	}

	// Set required headers for ETIMS API
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Accept", "application/json")
	httpReq.Header.Set("User-Agent", "Go-ETIMS-Signer/1.0.0")

	// Add ETIMS specific headers
	authHeaders := generateETIMSAuthHeaders(req)
	for key, value := range authHeaders {
		httpReq.Header.Set(key, value)
	}

	// Log headers for debugging
	log.Printf("DEBUG: Request headers:")
	for key, values := range httpReq.Header {
		log.Printf("  %s: %s", key, strings.Join(values, ", "))
	}

	// Log the request for debugging
	log.Printf("INFO: Posting to ETIMS API: %s", url)
	log.Printf("DEBUG: Request payload: %s", string(jsonData))

	// Make the HTTP request
	resp, err := etimsHTTPClient.Do(httpReq)
	if err != nil {
		log.Printf("ERROR: HTTP request failed: %v", err)
		return nil, fmt.Errorf("HTTP request failed: %v", err)
	}
	defer resp.Body.Close()

	// Read response body
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Printf("ERROR: Failed to read response body: %v", err)
		return nil, fmt.Errorf("failed to read response body: %v", err)
	}

	// Log response for debugging
	log.Printf("INFO: ETIMS API response status: %d", resp.StatusCode)
	log.Printf("DEBUG: Response body: %s", string(respBody))

	// Check if response is likely JSON by looking at first character
	if len(respBody) == 0 {
		log.Printf("WARN: Empty response body from ETIMS API")
		return nil, fmt.Errorf("empty response from ETIMS API")
	}

	// Handle non-JSON responses (like HTML error pages)
	firstChar := respBody[0]
	if firstChar != '{' && firstChar != '[' {
		log.Printf("WARN: Non-JSON response detected. First character: '%c' (ASCII: %d)", firstChar, firstChar)
		log.Printf("WARN: Response content: %s", string(respBody))

		// Try to extract meaningful error information
		responseStr := string(respBody)
		if strings.Contains(strings.ToLower(responseStr), "error") {
			return nil, fmt.Errorf("ETIMS API returned error response: %s", responseStr)
		}

		return nil, fmt.Errorf("ETIMS API returned non-JSON response starting with '%c': %s", firstChar, responseStr)
	}

	// Check HTTP status code
	if resp.StatusCode != http.StatusOK {
		log.Printf("WARN: ETIMS API returned non-200 status: %d", resp.StatusCode)
		return nil, fmt.Errorf("ETIMS API returned status %d: %s", resp.StatusCode, string(respBody))
	}

	// Parse JSON response
	var etimsResponse TrnsSalesSaveWrRes
	if err := json.Unmarshal(respBody, &etimsResponse); err != nil {
		log.Printf("ERROR: Failed to unmarshal ETIMS response: %v", err)
		log.Printf("ERROR: Response body that failed to parse: %s", string(respBody))
		return nil, fmt.Errorf("failed to parse ETIMS response as JSON: %v", err)
	}

	log.Printf("INFO: Successfully parsed ETIMS response. ResultCd: %s, ResultMsg: %s",
		etimsResponse.ResultCd, etimsResponse.ResultMsg)

	return &etimsResponse, nil
}

// Main handler for /trnsSales/saveSales endpoint
func saveTrnsSalesHandler(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	fmt.Println("Received request:", r.URL)

	// Only allow POST method
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Parse request
	var req TrnsSalesSaveWrReq
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		log.Printf("ERROR: Failed to parse request: %v", err)
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	// Validate required fields
	if req.Tin == "" || req.BhfId == "" {
		res := &TrnsSalesSaveWrRes{
			ResultCd:  RESULT_CODE_GENERAL_ERROR,
			ResultMsg: "Missing required parameters",
			ResultDt:  formatCurrentTime(),
		}
		json.NewEncoder(w).Encode(res)
		return
	}

	// Process the request
	res := saveTrnsSalesUltraFast(&req)
	json.NewEncoder(w).Encode(res)
}

// Ultra-fast sales transaction processing (matching Java implementation)
func saveTrnsSalesUltraFast(req *TrnsSalesSaveWrReq) *TrnsSalesSaveWrRes {
	tinBhfPath := req.Tin + "_" + req.BhfId

	// Get device data ultra-fast
	deviceData, err := getDeviceDataUltraFast(tinBhfPath)
	if err != nil {
		return &TrnsSalesSaveWrRes{
			ResultCd:  RESULT_CODE_GENERAL_ERROR,
			ResultMsg: "Failed to get device data: " + err.Error(),
			ResultDt:  formatCurrentTime(),
		}
	}

	// Get crypto keys ultra-fast
	cryptoKeys, err := getCryptoKeysUltraFast(tinBhfPath)
	if err != nil {
		return &TrnsSalesSaveWrRes{
			ResultCd:  RESULT_CODE_GENERAL_ERROR,
			ResultMsg: "Failed to get crypto keys: " + err.Error(),
			ResultDt:  formatCurrentTime(),
		}
	}

	// Validate sales type and receipt type
	salesType := req.SalesTyCd
	receiptType := req.RcptTyCd

	switch salesType {
	case SALES_TYPE_N:
		if receiptType != RECEIPT_TYPE_S && receiptType != RECEIPT_TYPE_R {
			return typeErrorResponse
		}
	case SALES_TYPE_T:
		if receiptType != RECEIPT_TYPE_S && receiptType != RECEIPT_TYPE_R {
			return typeErrorResponse
		}
	case SALES_TYPE_P:
		if receiptType != RECEIPT_TYPE_S {
			return typeErrorResponse
		}
	case SALES_TYPE_C:
		if receiptType != RECEIPT_TYPE_S && receiptType != RECEIPT_TYPE_R {
			return typeErrorResponse
		}
	default:
		return typeErrorResponse
	}

	// Generate sequence numbers
	rcptTpNo := getNextSequenceNumber()
	rcptNo := getNextSequenceNumber()
	rptNo := getNextSequenceNumber()

	if req.Receipt == nil {
		req.Receipt = &TrnsSalesSaveWrReceipt{}
	}

	req.Receipt.CurRcptNo = &rcptTpNo
	req.Receipt.TotRcptNo = &rcptNo
	req.Receipt.RptNo = &rptNo

	// Format current time
	vsdcRcptPbctDate := formatCurrentTime()
	req.Receipt.RcptPbctDt = vsdcRcptPbctDate

	// Generate internal data and signature for non-P/T types
	var intrlData, signData string
	if salesType != SALES_TYPE_P && salesType != SALES_TYPE_T {
		// Generate internal data
		intrlData, err = generateInternalData(salesType, receiptType, req.TaxAmtB, *req.Receipt.RptNo, req.InvcNo, cryptoKeys.HexKey)
		if err != nil {
			return &TrnsSalesSaveWrRes{
				ResultCd:  RESULT_CODE_GENERAL_ERROR,
				ResultMsg: "Failed to generate internal data: " + err.Error(),
				ResultDt:  formatCurrentTime(),
			}
		}

		// Generate signature
		signData, err = generateSignature(vsdcRcptPbctDate, req.Tin, req.CustTin, req.InvcNo, req.TaxblAmtB, req.TaxAmtB, req.TaxblAmtA, req.TaxAmtA, salesType, receiptType, req.InvcNo, req.InvcNo, deviceData.MrcNo, deviceData.SdcId, cryptoKeys.SignKey)
		if err != nil {
			return &TrnsSalesSaveWrRes{
				ResultCd:  RESULT_CODE_GENERAL_ERROR,
				ResultMsg: "Failed to generate signature: " + err.Error(),
				ResultDt:  formatCurrentTime(),
			}
		}
	}

	req.Receipt.IntrlData = intrlData
	req.Receipt.RcptSign = signData

	// Set sales status
	req.SalesSttsCd = "02"
	req.SalesTyCd = SALES_TYPE_N

	// Save data to postgres db
	salesId, err := saveSalesToDatabase(req, deviceData, intrlData, signData, vsdcRcptPbctDate)
	if err != nil {
		log.Printf("ERROR: Failed to save sales data to database: %v", err)
		return &TrnsSalesSaveWrRes{
			ResultCd:  RESULT_CODE_GENERAL_ERROR,
			ResultMsg: fmt.Sprintf("Failed to save to database: %v", err),
			ResultDt:  formatCurrentTime(),
		}
	}
	log.Printf("INFO: Successfully saved sales data to database with ID: %d", salesId)

	// Post to ETIMS API (if enabled)
	var etimsResponse *TrnsSalesSaveWrRes

	enableETIMSPosting = false

	if enableETIMSPosting {
		log.Printf("INFO: Posting sales data to ETIMS API for TIN: %s, BHF: %s, Invoice: %d",
			req.Tin, req.BhfId, req.InvcNo)

		// etimsResponse, err = postSalesToETIMS(req)
		// if err != nil {
		// 	log.Printf("ERROR: Failed to post sale to ETIMS API: %v", err)
		// 	return &TrnsSalesSaveWrRes{
		// 		ResultCd:  RESULT_CODE_GENERAL_ERROR,
		// 		ResultMsg: fmt.Sprintf("Failed to post to ETIMS: %v", err),
		// 		ResultDt:  formatCurrentTime(),
		// 	}
		// }

	} else {
		log.Printf("INFO: ETIMS posting disabled - generating local response for TIN: %s, BHF: %s, Invoice: %d",
			req.Tin, req.BhfId, req.InvcNo)

		// Generate local response when ETIMS posting is disabled
		etimsResponse = &TrnsSalesSaveWrRes{
			ResultCd:  RESULT_CODE_SUCCESS,
			ResultMsg: "Successful (Local Mode)",
			ResultDt:  formatCurrentTime(),
			Data: &TrnsSalesSaveWrResData{
				SdcId:            deviceData.SdcId,
				MrcNo:            deviceData.MrcNo,
				RcptNo:           req.Receipt.CurRcptNo,
				TotRcptNo:        req.Receipt.TotRcptNo,
				VsdcRcptPbctDate: vsdcRcptPbctDate,
				IntrlData:        intrlData,
				RcptSign:         signData,
				ResultDt:         formatCurrentTime(),
			},
		}
	}

	// If ETIMS returned an error, pass it through
	if etimsResponse.ResultCd != RESULT_CODE_SUCCESS {
		log.Printf("WARN: ETIMS API returned error. ResultCd: %s, ResultMsg: %s",
			etimsResponse.ResultCd, etimsResponse.ResultMsg)
		return etimsResponse
	}

	// Success - merge local data with ETIMS response
	if etimsResponse.Data == nil {
		etimsResponse.Data = &TrnsSalesSaveWrResData{}
	}

	// Ensure we have the local device data in the response
	etimsResponse.Data.SdcId = deviceData.SdcId
	etimsResponse.Data.MrcNo = deviceData.MrcNo
	etimsResponse.Data.VsdcRcptPbctDate = vsdcRcptPbctDate
	etimsResponse.Data.IntrlData = intrlData
	etimsResponse.Data.RcptSign = signData
	etimsResponse.Data.ResultDt = formatCurrentTime()

	// Use ETIMS receipt numbers if available, otherwise use local ones
	if etimsResponse.Data.RcptNo == nil {
		etimsResponse.Data.RcptNo = req.Receipt.CurRcptNo
	}
	if etimsResponse.Data.TotRcptNo == nil {
		etimsResponse.Data.TotRcptNo = req.Receipt.TotRcptNo
	}

	log.Printf("INFO: Successfully processed sales transaction. ETIMS ResultCd: %s",
		etimsResponse.ResultCd)

	return etimsResponse
}

// Debug handler to get pre-loaded device paths
func getPreloadedPathsHandler(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	// Only allow GET method
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	cacheMutex.RLock()
	devicePaths := make([]string, 0, len(preloadedDevice))
	for path := range preloadedDevice {
		devicePaths = append(devicePaths, path)
	}

	cryptoPaths := make([]string, 0, len(preloadedCrypto))
	for path := range preloadedCrypto {
		cryptoPaths = append(cryptoPaths, path)
	}
	cacheMutex.RUnlock()

	response := map[string]interface{}{
		"devicePaths": devicePaths,
		"cryptoPaths": cryptoPaths,
		"timestamp":   formatCurrentTime(),
	}

	json.NewEncoder(w).Encode(response)
}

// Handler to add a device path for pre-loading
func addDevicePathHandler(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	// Only allow POST method
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	tinBhfPath := r.URL.Query().Get("tinBhfPath")
	if tinBhfPath == "" {
		http.Error(w, "Missing tinBhfPath parameter", http.StatusBadRequest)
		return
	}

	// Add device data
	if err := preloadDeviceData(tinBhfPath); err != nil {
		response := map[string]string{
			"error":     "Failed to preload device data: " + err.Error(),
			"path":      tinBhfPath,
			"timestamp": formatCurrentTime(),
		}
		json.NewEncoder(w).Encode(response)
		return
	}

	// Add crypto keys
	if err := preloadCryptoKeys(tinBhfPath); err != nil {
		response := map[string]string{
			"error":     "Failed to preload crypto keys: " + err.Error(),
			"path":      tinBhfPath,
			"timestamp": formatCurrentTime(),
		}
		json.NewEncoder(w).Encode(response)
		return
	}

	response := map[string]string{
		"message":   "Successfully added device path: " + tinBhfPath,
		"path":      tinBhfPath,
		"timestamp": formatCurrentTime(),
	}

	json.NewEncoder(w).Encode(response)
}

// Configuration handler to manage ETIMS posting settings
func configHandler(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	switch r.Method {
	case http.MethodGet:
		// Get current configuration
		response := map[string]interface{}{
			"etimsPostingEnabled": enableETIMSPosting,
			"etimsApiBaseUrl":     ETIMS_API_BASE_URL,
			"etimsEndpoint":       ETIMS_SAVE_SALES_ENDPOINT,
			"httpTimeoutSeconds":  HTTP_TIMEOUT_SECONDS,
			"timestamp":           formatCurrentTime(),
		}
		json.NewEncoder(w).Encode(response)

	case http.MethodPost:
		// Update configuration
		var config struct {
			EnableETIMSPosting *bool `json:"enableETIMSPosting"`
		}

		if err := json.NewDecoder(r.Body).Decode(&config); err != nil {
			http.Error(w, "Invalid JSON", http.StatusBadRequest)
			return
		}

		if config.EnableETIMSPosting != nil {
			enableETIMSPosting = *config.EnableETIMSPosting
			log.Printf("INFO: ETIMS posting configuration updated. Enabled: %t", enableETIMSPosting)
		}

		response := map[string]interface{}{
			"message":             "Configuration updated successfully",
			"etimsPostingEnabled": enableETIMSPosting,
			"timestamp":           formatCurrentTime(),
		}
		json.NewEncoder(w).Encode(response)

	default:
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
	}
}

// Test headers handler to show what headers would be sent to ETIMS
func testHeadersHandler(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Parse request to generate headers
	var req TrnsSalesSaveWrReq
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	// Generate headers that would be sent to ETIMS
	authHeaders := generateETIMSAuthHeaders(&req)

	// Add standard HTTP headers
	allHeaders := map[string]string{
		"Content-Type": "application/json",
		"Accept":       "application/json",
		"User-Agent":   "Go-ETIMS-Signer/1.0.0",
	}

	// Merge auth headers
	for key, value := range authHeaders {
		allHeaders[key] = value
	}

	response := map[string]interface{}{
		"message":   "Headers that would be sent to ETIMS API",
		"url":       ETIMS_API_BASE_URL + ETIMS_SAVE_SALES_ENDPOINT,
		"headers":   allHeaders,
		"timestamp": formatCurrentTime(),
	}

	json.NewEncoder(w).Encode(response)
}

// Health check handler
func healthHandler(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	// Only allow GET method
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	cacheMutex.RLock()
	deviceCount := len(preloadedDevice)
	cryptoCount := len(preloadedCrypto)
	cacheMutex.RUnlock()

	response := map[string]interface{}{
		"status":           "healthy",
		"service":          "Go ETIMS Signer",
		"version":          "1.0.0",
		"timestamp":        formatCurrentTime(),
		"preloadedDevices": deviceCount,
		"preloadedCrypto":  cryptoCount,
		"sequenceCounter":  sequenceCounter,
	}

	json.NewEncoder(w).Encode(response)
}
